import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def sigmoid(x):
    return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

def tanh_activation(x):
    return np.tanh(x)

def relu(x):
    return np.maximum(0, x)

def swish(x):
    return x * sigmoid(x)

def complex_activation_landscape(x, y):
    r = np.sqrt(x**2 + y**2)
    theta = np.arctan2(y, x)
    
    radial_component = np.exp(-0.3 * r) * np.cos(2 * theta)
    
    gate1 = sigmoid(2 * x - 1)
    gate2 = sigmoid(2 * y + 0.5)
    
    nonlinear_x = tanh_activation(1.5 * x + 0.5 * y)
    nonlinear_y = tanh_activation(1.5 * y - 0.5 * x)
    
    relu_region = relu(x + y - 1) * relu(2 - x - y)
    
    swish_x = swish(0.8 * x)
    swish_y = swish(0.8 * y)
    
    z = (0.6 * radial_component * gate1 * gate2 + 
         0.5 * nonlinear_x * nonlinear_y + 
         0.3 * relu_region + 
         0.2 * (swish_x + swish_y))
    
    high_freq = 0.05 * np.sin(8 * x) * np.cos(8 * y) * np.exp(-0.1 * r)
    
    return z + high_freq

x = np.linspace(-5, 5, 100)
y = np.linspace(-5, 5, 100)
X, Y = np.meshgrid(x, y)
Z = complex_activation_landscape(X, Y)

fig = plt.figure(figsize=(12, 10))
ax = fig.add_subplot(111, projection='3d')
surf = ax.plot_surface(X, Y, Z, cmap='viridis', alpha=0.9)
ax.set_title('复合激活函数景观')
plt.savefig("C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/26/option_B.png")
plt.show()

